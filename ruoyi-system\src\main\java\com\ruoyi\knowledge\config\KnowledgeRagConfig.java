package com.ruoyi.knowledge.config;

import com.ruoyi.knowledge.search.HybridSearchStrategy;
import com.ruoyi.knowledge.search.KeywordSearchEngine;
import com.ruoyi.knowledge.search.impl.DefaultHybridSearchStrategy;
import com.ruoyi.knowledge.search.impl.InMemoryKeywordSearchEngine;
import com.ruoyi.knowledge.store.CustomMilvusEmbeddingStore;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.embedding.onnx.allminilml6v2.AllMiniLmL6V2EmbeddingModel;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.redis.RedisEmbeddingStore;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import dev.langchain4j.store.embedding.milvus.MilvusEmbeddingStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 知识库RAG配置类
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Configuration
public class KnowledgeRagConfig {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeRagConfig.class);

    @Value("${spring.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.database:0}")
    private int redisDatabase;

    @Value("${knowledge.embedding.store.type:memory}")
    private String storeType;

    @Value("${knowledge.hybrid.search.enabled:true}")
    private boolean hybridSearchEnabled;

    @Value("${knowledge.hybrid.search.vector.weight:0.7}")
    private double defaultVectorWeight;

    @Value("${knowledge.hybrid.search.keyword.weight:0.3}")
    private double defaultKeywordWeight;

    @Autowired(required = false)
    private MilvusConfig milvusConfig;

    @Autowired(required = false)
    private CustomMilvusEmbeddingStore customMilvusEmbeddingStore;

    /**
     * 嵌入模型Bean
     * 使用本地的AllMiniLmL6V2模型，无需API调用
     */
    @Bean
    public EmbeddingModel embeddingModel() {
        return new AllMiniLmL6V2EmbeddingModel();
    }

    /**
     * 向量存储Bean
     * 支持Redis、Milvus和内存三种存储方式
     */
    @Bean
    public EmbeddingStore<TextSegment> embeddingStore() {
        if ("redis".equalsIgnoreCase(storeType)) {
            // 使用Redis存储（需要RediSearch模块）
            RedisEmbeddingStore.Builder builder = RedisEmbeddingStore.builder()
                    .host(redisHost)
                    .port(redisPort)
                    .dimension(384) // AllMiniLmL6V2模型的向量维度是384
                    .indexName("knowledge_base_index"); // 指定索引名称

            // 如果有密码则设置密码
            if (redisPassword != null && !redisPassword.trim().isEmpty()) {
                builder.password(redisPassword);
            }

            return builder.build();
        } else if ("milvus".equalsIgnoreCase(storeType)) {
            // 使用Milvus存储
            logger.info("配置使用Milvus存储");

            if (customMilvusEmbeddingStore != null) {
                logger.info("使用CustomMilvusEmbeddingStore实例");
                return customMilvusEmbeddingStore;
            } else {
                logger.warn("CustomMilvusEmbeddingStore未初始化，可能是Milvus连接失败");
                logger.warn("回退到内存存储，请检查Milvus服务是否正常运行");
                return new InMemoryEmbeddingStore<>();
            }
        } else {
            // 使用内存存储（默认，不需要Redis模块）
            return new InMemoryEmbeddingStore<>();
        }
    }

    /**
     * 内容检索器Bean
     */
    @Bean
    public ContentRetriever contentRetriever(EmbeddingStore<TextSegment> embeddingStore,
            EmbeddingModel embeddingModel) {
        return EmbeddingStoreContentRetriever.builder()
                .embeddingStore(embeddingStore)
                .embeddingModel(embeddingModel)
                .maxResults(5) // 最多返回5个相关结果
                .minScore(0.6) // 最小相似度分数
                .build();
    }

    /**
     * 文档分割器Bean
     * 用于将长文档分割成小段落
     */
    @Bean
    public DocumentSplitter documentSplitter() {
        return DocumentSplitters.recursive(
                500, // 每段最大字符数
                50 // 段落间重叠字符数
        );
    }

    /**
     * 关键词搜索引擎Bean
     */
    @Bean
    public KeywordSearchEngine keywordSearchEngine() {
        return new InMemoryKeywordSearchEngine();
    }

    /**
     * 混合检索策略Bean
     */
    @Bean
    public HybridSearchStrategy hybridSearchStrategy() {
        return new DefaultHybridSearchStrategy();
    }
}
